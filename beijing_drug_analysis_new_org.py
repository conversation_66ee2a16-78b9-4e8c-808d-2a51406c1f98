import pymysql
import pandas as pd
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class BeijingDrugAnalyzer:
    def __init__(self, host, port, user, password, database):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        # 加载药物数据
        self.drug1_list = self.load_drug1_list()
        self.drug2_list = self.load_drug2_list()
        self.drug_keywords = self.load_drug_keywords()

    def load_drug1_list(self):
        """从drug_com.xlsx的Sheet1加载第1种药物列表"""
        try:
            df = pd.read_excel('drug_com.xlsx', sheet_name='Sheet1')
            drug1_list = []
            
            for _, row in df.iterrows():
                drug_name = str(row.iloc[0]).strip()  # 第1列
                if pd.isna(drug_name) or drug_name == 'nan':
                    continue
                drug1_list.append(drug_name)
            
            print(f"成功加载 {len(drug1_list)} 种第1类药物")
            return drug1_list
            
        except Exception as e:
            print(f"加载第1类药物数据失败: {str(e)}")
            return []

    def load_drug2_list(self):
        """从drug_com.xlsx的Sheet2加载第2种药物列表"""
        try:
            df = pd.read_excel('drug_com.xlsx', sheet_name='Sheet2')
            drug2_list = []
            
            for _, row in df.iterrows():
                drug_name = str(row.iloc[0]).strip()  # 第1列
                if pd.isna(drug_name) or drug_name == 'nan':
                    continue
                drug2_list.append(drug_name)
            
            print(f"成功加载 {len(drug2_list)} 种第2类药物")
            return drug2_list
            
        except Exception as e:
            print(f"加载第2类药物数据失败: {str(e)}")
            return []

    def load_drug_keywords(self):
        """从drug_info.xlsx的Sheet1加载药物关键词"""
        try:
            df = pd.read_excel('drug_info.xlsx', sheet_name='Sheet1')
            drug_keywords = {}
            
            for _, row in df.iterrows():
                drug_name = str(row.iloc[0]).strip()  # 第1列
                if pd.isna(drug_name) or drug_name == 'nan':
                    continue
                
                # 获取第2-3列的关键词
                keywords = []
                for i in range(1, 3):  # 第2-3列
                    keyword_text = str(row.iloc[i]).strip() if not pd.isna(row.iloc[i]) else ""
                    if keyword_text and keyword_text != 'nan':
                        # 按分号分隔关键词
                        keyword_list = []
                        for separator in [';', '；']:
                            if separator in keyword_text:
                                keyword_list.extend(keyword_text.split(separator))
                                break
                        else:
                            # 如果没有分号，整个文本作为一个关键词
                            keyword_list = [keyword_text]
                        
                        # 处理每个关键词
                        for keyword in keyword_list:
                            keyword = keyword.strip()
                            if keyword and keyword != 'nan':
                                # 过滤掉英文关键词（只保留中文关键词）
                                if self.is_chinese_keyword(keyword):
                                    keywords.append(keyword)
                
                drug_keywords[drug_name] = keywords
            
            print(f"成功加载 {len(drug_keywords)} 种药物的关键词数据")
            return drug_keywords
            
        except Exception as e:
            print(f"加载药物关键词数据失败: {str(e)}")
            return {}

    def is_chinese_keyword(self, keyword):
        """判断是否为中文关键词（过滤掉纯英文关键词）"""
        if not keyword:
            return False
        
        # 检查是否包含中文字符
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in keyword)
        
        # 如果包含中文字符，认为是中文关键词
        if has_chinese:
            return True
        
        # 如果只包含英文、数字、符号等，过滤掉
        return False

    def connect(self):
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print("数据库连接成功！")
            return True
        except Exception as e:
            print(f"数据库连接失败: {str(e)}")
            return False

    def disconnect(self):
        if self.connection:
            self.connection.close()
            print("数据库连接已关闭")

    def execute_query(self, query, params=None):
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"查询执行失败: {str(e)}")
            return []

    def get_daxing_tables(self):
        query = "SHOW TABLES LIKE 'daxing%'"
        tables = self.execute_query(query)
        table_names = [list(table.values())[0] for table in tables]
        print(f"找到 {len(table_names)} 个daxing开头的表: {table_names}")
        return table_names

    def safe_table_name(self, table_name):
        """安全处理表名，为包含特殊字符的表名添加反引号"""
        if table_name.startswith('`') and table_name.endswith('`'):
            return table_name
        return f"`{table_name}`"

    def get_table_structure(self, table_name):
        query = f"DESCRIBE {self.safe_table_name(table_name)}"
        return self.execute_query(query)

    def detect_drugs_in_text(self, text):
        """从文本中检测药物"""
        if not text:
            return []
        
        detected_drugs = []
        text_lower = text.lower()
        
        for drug_name, keywords in self.drug_keywords.items():
            for keyword in keywords:
                if keyword and keyword.lower() in text_lower:
                    detected_drugs.append(drug_name)
                    break  # 找到一个关键词就认为检测到该药物
        
        return detected_drugs

    def get_patient_drug_data(self):
        print("正在获取患者药物数据...")
        daxing_tables = self.get_daxing_tables()
        patient_drugs = defaultdict(set)
        
        for table_name in daxing_tables:
            print(f"正在从表 {table_name} 获取药物数据...")
            columns = self.get_table_structure(table_name)
            column_names = [col['Field'] for col in columns]
            
            # 查找患者ID列
            patient_id_column = None
            for col in column_names:
                if any(k in col.lower() for k in ['patient', 'pat', 'id', 'person']):
                    patient_id_column = col
                    break
            
            if not patient_id_column:
                continue
            
            # 查找可能包含药物信息的列
            text_columns = []
            for col in column_names:
                col_lower = col.lower()
                if any(k in col_lower for k in ['drug', 'medicine', 'medication', 'prescription', 'order', 'drug_name', 'med_name', 'note', 'desc', 'content', 'text', 'result', 'finding']):
                    text_columns.append(col)
            
            if not text_columns:
                continue
            
            select_columns = [patient_id_column] + text_columns
            query = f"""
            SELECT {', '.join(select_columns)}
            FROM {self.safe_table_name(table_name)}
            """
            
            try:
                records = self.execute_query(query)
                drug_count = 0
                
                for record in records:
                    patient_id = str(record[patient_id_column])
                    
                    # 合并所有文本字段
                    text_content = ' '.join([
                        str(record[col]) for col in text_columns 
                        if record.get(col) and str(record[col]).strip()
                    ])
                    
                    # 检测药物
                    detected_drugs = self.detect_drugs_in_text(text_content)
                    
                    for drug in detected_drugs:
                        patient_drugs[patient_id].add(drug)
                        drug_count += 1
                
                print(f"  从表 {table_name} 获取到 {len(records)} 条记录，检测到 {drug_count} 个药物")
                
            except Exception as e:
                print(f"  查询表 {table_name} 时出错: {str(e)}")
                if "Unknown column" in str(e) or "syntax" in str(e).lower():
                    print(f"  跳过表 {table_name}（表结构不兼容）")
                    continue
        
        print(f"总共获取到 {len(patient_drugs)} 个患者的药物数据")
        return dict(patient_drugs)

    def analyze_drug_cooccurrence(self, patient_drugs):
        print("正在分析药物共现情况...")
        
        # 统计药物共现
        cooccurrence_stats = Counter()
        detailed_cooccurrence = defaultdict(list)
        
        for patient_id, drugs in patient_drugs.items():
            # 分离第1类药物和第2类药物
            drug1_found = [d for d in drugs if d in self.drug1_list]
            drug2_found = [d for d in drugs if d in self.drug2_list]
            
            # 统计共现情况
            for drug1 in drug1_found:
                for drug2 in drug2_found:
                    cooccurrence_key = (drug1, drug2)
                    cooccurrence_stats[cooccurrence_key] += 1
                    
                    # 记录详细信息
                    detailed_cooccurrence[cooccurrence_key].append({
                        'patient_id': patient_id,
                        'drug1': drug1,
                        'drug2': drug2,
                        'drug1_keywords': self.drug_keywords.get(drug1, []),
                        'drug2_keywords': self.drug_keywords.get(drug2, [])
                    })
        
        return {
            'cooccurrence_stats': dict(cooccurrence_stats),
            'detailed_cooccurrence': dict(detailed_cooccurrence)
        }

    def generate_report(self, analysis_result):
        """生成分析报告"""
        print("\n" + "="*80)
        print("北京大兴区药物共现分析报告")
        print("="*80)
        
        cooccurrence_stats = analysis_result['cooccurrence_stats']
        detailed_cooccurrence = analysis_result['detailed_cooccurrence']
        
        if not cooccurrence_stats:
            print("\n未发现药物共现情况")
            return
        
        print(f"\n总共发现 {len(cooccurrence_stats)} 种药物共现组合")
        
        # 按共现次数排序
        sorted_cooccurrences = sorted(cooccurrence_stats.items(), key=lambda x: x[1], reverse=True)
        
        print("\n药物共现统计（前20种）:")
        print("-" * 80)
        for i, ((drug1, drug2), count) in enumerate(sorted_cooccurrences[:20], 1):
            print(f"{i:2d}. {drug1} + {drug2}: {count}次")
        
        # 输出详细统计信息
        print("\n详细统计信息:")
        print("-" * 80)
        total_records = sum(len(details) for details in detailed_cooccurrence.values())
        print(f"总详细记录数: {total_records}")
        
        # 按药物组合统计记录数
        print("\n各药物组合详细记录数（前30种）:")
        sorted_details = sorted(detailed_cooccurrence.items(), 
                              key=lambda x: len(x[1]), reverse=True)
        for i, ((drug1, drug2), details) in enumerate(sorted_details[:30], 1):
            print(f"{i:2d}. {drug1} + {drug2}: {len(details)}条记录")
        
        # 如果数据量过大，提供建议
        if total_records > 1000000:
            print(f"\n注意：数据量过大（{total_records}条记录），建议：")
            print("1. 查看控制台输出的详细统计信息")
            print("2. 考虑按药物类型或时间范围进行分批分析")
            print("3. 使用数据库查询工具直接查看完整数据")

    def save_to_excel(self, analysis_result, filename="beijing_drug_analysis_new.xlsx"):
        """将分析结果保存到Excel文件"""
        print(f"\n正在保存分析结果到 {filename}...")
        
        cooccurrence_stats = analysis_result['cooccurrence_stats']
        detailed_cooccurrence = analysis_result['detailed_cooccurrence']
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Sheet1: 药物共现统计
            if cooccurrence_stats:
                cooccurrence_data = [
                    {
                        '第1类药物': drug1,
                        '第2类药物': drug2,
                        '统计数量': count
                    }
                    for (drug1, drug2), count in cooccurrence_stats.items()
                ]
                cooccurrence_df = pd.DataFrame(cooccurrence_data)
                cooccurrence_df = cooccurrence_df.sort_values('统计数量', ascending=False)
            else:
                cooccurrence_df = pd.DataFrame(columns=['第1类药物', '第2类药物', '统计数量'])
            
            cooccurrence_df.to_excel(writer, sheet_name='Sheet1', index=False)
            
            # Sheet2: 详细统计情况（限制数据量避免Excel过大）
            detailed_data = []
            for (drug1, drug2), details in detailed_cooccurrence.items():
                # 限制每种药物组合最多保存10000条记录
                limited_details = details[:10000]
                for detail in limited_details:
                    detailed_data.append({
                        '第1类药物': detail['drug1'],
                        '第2类药物': detail['drug2'],
                        '患者ID': detail['patient_id'],
                        '第1类药物关键词': '; '.join(detail['drug1_keywords']),
                        '第2类药物关键词': '; '.join(detail['drug2_keywords'])
                    })
            
            if detailed_data:
                detailed_df = pd.DataFrame(detailed_data)
                detailed_df = detailed_df.sort_values(['第1类药物', '第2类药物', '患者ID'])
                # 如果数据量仍然过大，进一步限制
                if len(detailed_df) > 1000000:  # Excel最大行数限制
                    print(f"警告：详细数据量过大（{len(detailed_df)}条），将只保存前100万条记录")
                    detailed_df = detailed_df.head(1000000)
            else:
                detailed_df = pd.DataFrame(columns=['第1类药物', '第2类药物', '患者ID', '第1类药物关键词', '第2类药物关键词'])
            
            detailed_df.to_excel(writer, sheet_name='Sheet2', index=False)
        
        print(f"分析结果已保存到 {filename}")
        print(f"Sheet1包含 {len(cooccurrence_stats)} 种药物共现组合")
        print(f"Sheet2包含 {len(detailed_data)} 条详细记录")
        
        # 如果数据量过大，输出统计信息
        total_detailed_records = sum(len(details) for details in detailed_cooccurrence.values())
        if total_detailed_records > 1000000:
            print(f"注意：详细数据总量为 {total_detailed_records} 条，Excel中只保存了前 100万 条记录")
            print("如需完整数据，请查看控制台输出的详细报告")

    def run_analysis(self):
        """完整的分析流程"""
        if not self.connect():
            return
        
        try:
            # 1. 获取患者药物数据
            patient_drugs = self.get_patient_drug_data()
            
            if not patient_drugs:
                print("未找到患者药物数据")
                return
            
            # 2. 分析药物共现
            analysis_result = self.analyze_drug_cooccurrence(patient_drugs)
            
            # 3. 生成报告
            self.generate_report(analysis_result)
            
            # 4. 保存到Excel
            self.save_to_excel(analysis_result)
            
        finally:
            self.disconnect()

def main():
    """主函数"""
    # 数据库连接参数
    host = "***************"
    port = 13306
    user = "root"
    password = "root"
    database = "data_beijing"
    
    # 创建分析器并运行分析
    analyzer = BeijingDrugAnalyzer(host, port, user, password, database)
    analyzer.run_analysis()

if __name__ == "__main__":
    main() 